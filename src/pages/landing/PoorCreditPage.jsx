import { lazy, Suspense } from "react";
import { PreQualificationForm } from "../../components/PreQualificationForm/PreQualificationForm.jsx";
import {
  QuickLinksSectionSkeleton,
  PrequalifyFAQSkeleton,
  FundingStepsSkeleton,
  TrustpilotReviewsSkeleton,
} from "../../components/ui/Skeletons.jsx";
import PreQualifyExplainer from "../../components/ui/PreQualifyExplainer.jsx";

import FAQSection from "../../components/shared/FAQSection.jsx";
import FundingStepsSection from "../../components/shared/FundingStepsSection";

const TrustpilotReviews = lazy(() => import("../../components/ui/TrustpilotReviews.jsx"));
const QuickLinksSection = lazy(() => import("../../components/ui/QuickLinksSection.jsx"));

const faqItems = [
  {
    question: "What do I need to qualify for Business Funding with Poor Credit?",
    answer: (
      <ul className="space-y-1.5">
        <li className="flex items-baseline">
          <span className="mr-1.5 flex-shrink-0">•</span>
          <span>Minimum six months in business.</span>
        </li>
        <li className="flex items-baseline">
          <span className="mr-1.5 flex-shrink-0">•</span>
          <span>Minimum of $180,000 in annual revenue</span>
        </li>
        <li className="flex items-baseline">
          <span className="mr-1.5 flex-shrink-0">•</span>
          <span>All credit scores are accepted</span>
        </li>
        <li className="flex items-baseline">
          <span className="mr-1.5 flex-shrink-0">•</span>
          <span>Business checking account</span>
        </li>
      </ul>
    ),
  },
  {
    question: "What is the application process for Pinnacle Funding?",
    answer: (
      <p>
        We pride ourselves on our quick and simple application process. It takes under 5 minutes to fill out our online
        application. We will ask for basic information on you and your business to get you the best offer. You can also
        apply over the phone by calling (*************.
      </p>
    ),
  },
  {
    question: "How soon can I get my funds?",
    answer: (
      <p>
        Pinnacle offers fast business funding, with approved businesses typically able to get Same Day Business Funding
        within 24 hours of completing a full application.
      </p>
    ),
  },
  {
    question: "Will there be a hard credit pull?",
    answer: (
      <p>
        We believe in simple and stress-free funding with no hard credit pull. There are no fees, no collateral, and no
        credit impact when you apply.
      </p>
    ),
  },
];

const steps = [
  {
    title: "Apply Online",
    description: "Fill out our quick, 5-minute application—no long paperwork, no hassle, and no hard credit pull.",
  },
  {
    title: "Review & Decide",
    description:
      "Your dedicated funding specialist walks you through your offers and helps you choose what’s right for your business.",
  },
  {
    title: "Get Funded",
    description: "Receive Funding for your business in as little as 24 hours - fast, simple, and secure.",
  },
  {
    title: "Grow Together",
    description:
      "Over 90% of qualified clients draw again from their existing Revolving Line of Credit. We’re here for the long haul.",
  },
];

const FundingSteps = () => <FundingStepsSection steps={steps} />;

const PrequalifyFAQ = () => <FAQSection faqItems={faqItems} ctaTitle="Apply for Fast Business Loans" />;

export const PoorCreditPage = () => {
  return (
    <div>
      <title>Business Loans for Poor Credit | Pinnacle Funding</title>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:space-x-8 py-4 lg:py-8">
          <div className="w-full lg:w-1/3 mb-6 lg:mb-0">
            <PreQualifyExplainer title="Ready to Grow Your Business?" />
          </div>
          <div className="w-full lg:w-2/3">
            <div id="prequalify-form-scroll-target" className="scroll-mt-8" />
            <PreQualificationForm
              header={"Business Loans for Poor Credit"}
              subheader={"Get Pre-Approved In Minutes"}
              firstStepTitle={"Let's Get you Tailored Funding"}
            />
          </div>
        </div>
        <Suspense fallback={<FundingStepsSkeleton />}>
          <FundingSteps />
        </Suspense>
        <Suspense fallback={<TrustpilotReviewsSkeleton />}>
          <TrustpilotReviews title="Trusted Funding for All Credit Types" />
        </Suspense>
        <Suspense fallback={<PrequalifyFAQSkeleton />}>
          <PrequalifyFAQ />
        </Suspense>
        <Suspense fallback={<QuickLinksSectionSkeleton />}>
          <QuickLinksSection />
        </Suspense>
      </div>
    </div>
  );
};

export default PoorCreditPage;
