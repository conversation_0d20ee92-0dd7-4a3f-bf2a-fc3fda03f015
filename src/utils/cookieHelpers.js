/**
 * Helper function to get the root domain for cookie sharing across subdomains
 * @returns {string} Root domain
 */
export const getRootDomain = () => {
  const hostname = window.location.hostname;

  // For localhost or IP addresses, don't set domain
  if (hostname === "localhost" || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // Split hostname and get the last two parts (domain.tld)
  const parts = hostname.split(".");
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join(".")}`;
  }

  return null;
};

/**
 * Helper function to set a cookie
 * @param {string} name - Cookie name
 * @param {any} value - Cookie value
 * @param {Object} options - Cookie options
 * @param {number} options.days - Cookie expiration in days (default: 7)
 * @param {string} options.path - Cookie path (default: '/')
 * @param {string} options.domain - Cookie domain (default: root domain for subdomain sharing)
 * @param {boolean} options.secure - Whether cookie should only be sent over HTTPS (default: true in production)
 * @param {boolean} options.sameSite - SameSite attribute (default: 'Lax')
 */
export function setCookieHelper(name, value, options = {}) {
  const {
    days = 7,
    path = "/",
    domain = getRootDomain(),
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;

  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

  let cookieValue;

  if (typeof value === "string") {
    cookieValue = encodeURIComponent(value);
  } else {
    cookieValue = encodeURIComponent(JSON.stringify(value));
  }

  document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }${secure ? "; Secure" : ""}; SameSite=${sameSite}`;
}

/**
 * Helper function to get a cookie by name
 * @param {string} name - Cookie name
 * @returns {any} Cookie value or null if not found
 */
export function getCookieHelper(name) {
  if (typeof window === "undefined") return null;

  const cookies = document.cookie.split("; ");
  const cookie = cookies.find((c) => c.startsWith(`${name}=`));

  if (!cookie) return null;

  const cookieValue = cookie.split("=")[1];

  try {
    return JSON.parse(decodeURIComponent(cookieValue));
  } catch {
    return decodeURIComponent(cookieValue);
  }
}

/**
 * Helper function to remove a cookie
 * @param {string} name - Cookie name
 * @param {Object} options - Cookie options
 * @param {string} options.path - Cookie path (default: '/')
 * @param {string} options.domain - Cookie domain (default: root domain)
 */
export function removeCookieHelper(name, options = {}) {
  const { path = "/", domain = getRootDomain() } = options;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }`;
}

/**
 * Set an individual UTM parameter cookie
 * @param {string} utmKey - UTM parameter key (e.g., 'utm_source')
 * @param {string} value - UTM parameter value
 * @param {Object} options - Cookie options
 */
export function setUtmCookie(utmKey, value, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  setCookieHelper(utmKey, value, options);
}

/**
 * Get an individual UTM parameter cookie
 * @param {string} utmKey - UTM parameter key (e.g., 'utm_source')
 * @returns {string|null} UTM parameter value or null if not found
 */
export function getUtmCookie(utmKey) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  return getCookieHelper(utmKey);
}

/**
 * Remove an individual UTM parameter cookie
 * @param {string} utmKey - UTM parameter key (e.g., 'utm_source')
 * @param {Object} options - Cookie options
 */
export function removeUtmCookie(utmKey, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  removeCookieHelper(utmKey, options);
}

/**
 * Collect all UTM parameter cookies into an object
 * @returns {Object} Object containing all UTM parameters
 */
export function collectUtmCookies() {
  if (typeof window === "undefined") return {};

  const utmParams = {};
  const cookies = document.cookie.split("; ");

  cookies.forEach((cookie) => {
    const [name, value] = cookie.split("=");
    if (name && name.startsWith("utm_") && value) {
      try {
        utmParams[name] = String(JSON.parse(decodeURIComponent(value)));
      } catch {
        utmParams[name] = decodeURIComponent(value);
      }
    }
  });

  return utmParams;
}
