import { logger } from "./logger";
import { collectUtmCookies, setCookie<PERSON>elper, getCookie<PERSON>elper } from "./cookieHelpers";

export const AdwordsConversions = {
  PREQUAL_APPROVED: "AW-16778466762/Bv7HCMSRkO0aEMqrzMA-",
  PREQUAL_DENIED: "AW-16778466762/qqEiCIn0ne0aEMqrzMA-",
};

const log = (...args) => {
  logger.log("[AdWords]: ", ...args);
};

export function trackConversion(key) {
  const utmParams = collectUtmCookies();
  if (utmParams?.utm_source?.toLowerCase() !== "googleadwords") return;

  const conversionId = AdwordsConversions[key];
  if (!conversionId) {
    log(`Unknown Adwords conversion key: ${key}`);
    return;
  }

  // use shared key for prequal conversions
  const isPrequal = key === "PREQUAL_APPROVED" || key === "PREQUAL_DENIED";
  const cookieKey = isPrequal ? "aw_tracked_PREQUAL_RESULT" : `aw_tracked_${key}`;

  if (getCookieHelper(cookieKey)) {
    log("Skipping already tracked adwords conversion:", key);
    return;
  }

  if (!window.gtag) return;

  setCookieHelper(cookieKey, "true", { days: 1 });

  log("Tracking Adwords Conversion:", conversionId);
  window.gtag("event", "conversion", { send_to: conversionId });
}
