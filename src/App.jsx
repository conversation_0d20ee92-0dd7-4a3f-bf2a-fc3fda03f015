import { useEffect, lazy, Suspense, useState } from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { useQueryParamsLoad } from "./hooks/useQueryParamsLoad.js";
import { Header } from "./components/ui/Header.jsx";
import { PreQualifyPage } from "./pages/PreQualifyPage.jsx";
import { trackCustomEvent } from "./utils/analytics.js";
import { IS_DEV_MODE } from "./utils/consts";
import { FooterSkeleton } from "./components/ui/Skeletons.jsx";
import { collectUtmCookies, removeCookieHelper, setUtmCookie } from "./utils/cookieHelpers";
import { logger } from "./utils/logger.js";
import useLoadAnalytics from "./hooks/useLoadAnalytics.js";
import { LoaderCircle } from "lucide-react";

const AppFormPage = lazy(() => import("./pages/AppFormPage.jsx"));
const FastTrackPage = lazy(() => import("./pages/FastTrackPage.jsx"));
const UploadPage = lazy(() => import("./pages/UploadPage.jsx"));
const NotFoundPage = lazy(() => import("./pages/NotFoundPage.jsx"));
const PreQualifyResult = lazy(() => import("./components/PreQualifyResult/PreQualifyResult.jsx"));
const ApplicationResult = lazy(() => import("./components/ApplicationResult/ApplicationResult.jsx"));

// Lazy load landing pages
const LineOfCreditPage = lazy(() => import("./pages/landing/LineOfCreditPage.jsx"));
const MedicalPracticePage = lazy(() => import("./pages/landing/MedicalPracticePage.jsx"));
const EcommercePage = lazy(() => import("./pages/landing/EcommercePage.jsx"));
const AutoShopPage = lazy(() => import("./pages/landing/AutoShopPage.jsx"));
const PoorCreditPage = lazy(() => import("./pages/landing/PoorCreditPage.jsx"));

const Footer = lazy(() => import("./components/ui/Footer.jsx"));

function trackUtmParameters(utmParams) {
  if (!utmParams || Object.keys(utmParams).length === 0) return;

  // Track each UTM parameter as a separate event
  Object.entries(utmParams).forEach(([key, value]) => {
    if (value) {
      trackCustomEvent(key, value);
    }
  });
}

// Helper to check debug mode (same as logger.js)
function isDebugMode() {
  if (import.meta.env.VITE_DEBUG_MODE === "true") return true;
  if (typeof window === "undefined") return false;
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get("debug") === "true";
}

function DebugUtmBox() {
  const [utmCookies, setUtmCookies] = useState({});
  const [closed, setClosed] = useState(false);

  useEffect(() => {
    if (closed) return;
    const update = () => setUtmCookies(collectUtmCookies());
    update();
    const interval = setInterval(update, 2000);
    return () => clearInterval(interval);
  }, [closed]);

  const handleClose = () => {
    setClosed(true);
    sessionStorage.setItem("utmBoxClosed", "true");
  };

  if (closed || !Object.keys(utmCookies).length) return null;

  const handleClearAll = () => {
    Object.keys(utmCookies).forEach((name) => removeCookieHelper(name));
    setUtmCookies({});
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: 16,
        right: 16,
        background: "rgba(0,0,0,0.75)",
        color: "#fff",
        padding: "12px 16px",
        borderRadius: 8,
        fontSize: 13,
        zIndex: 10000,
        maxWidth: "92%",
        boxShadow: "0 2px 8px rgba(0,0,0,0.2)",
        fontFamily: "monospace",
      }}
    >
      <button
        onClick={handleClose}
        style={{
          position: "absolute",
          top: 4,
          right: 8,
          background: "none",
          border: "none",
          color: "#aaa",
          fontSize: 14,
          cursor: "pointer",
        }}
        aria-label="Close debug UTM box"
        title="Close"
      >
        ×
      </button>
      <div style={{ fontWeight: "bold", marginBottom: 4 }}>UTM Cookies</div>
      <pre style={{ margin: 0, whiteSpace: "pre-wrap", wordBreak: "break-all" }}>
        {Object.entries(utmCookies)
          .map(([k, v]) => `${k}: ${v}`)
          .join("\n")}
      </pre>
      <button
        onClick={handleClearAll}
        style={{
          marginTop: 8,
          background: "#fff",
          color: "rgba(0,0,0,0.75)",
          border: "none",
          borderRadius: 4,
          padding: "2px 6px",
        }}
      >
        Clear All
      </button>
    </div>
  );
}

const PageLoading = () => {
  return (
    <div className={`flex justify-center items-center h-full`}>
      <LoaderCircle size={32} color="#23448F" className="animate-spin" />
    </div>
  );
};

// eslint-disable-next-line no-unused-vars
const lazyLoadPage = (Component) => {
  return (
    <Suspense fallback={<PageLoading />}>
      <Component />
    </Suspense>
  );
};

/**
 * Main application component
 * @returns {JSX.Element}
 */
function App() {
  const analyticsLoaded = useLoadAnalytics();
  const { utmParams } = useQueryParamsLoad();

  if (IS_DEV_MODE) {
    console.log("Dev Mode!");
    console.table(import.meta.env);
  }

  useEffect(() => {
    if (document.referrer) {
      setUtmCookie("utm_referrer", document.referrer);
    }
  }, []);

  // Track UTM parameters when the app loads
  useEffect(() => {
    if (analyticsLoaded) {
      logger.log("Analytics loaded, tracking UTM parameters");

      if (utmParams && Object.keys(utmParams).length > 0) {
        trackUtmParameters(utmParams);
      }
    }
  }, [analyticsLoaded, utmParams]);

  return (
    <Router>
      <div className="min-h-screen font-lexend bg-gray-100">
        <Header />

        {IS_DEV_MODE && (
          <div
            style={{
              position: "fixed",
              bottom: 16,
              left: 16,
              background: "rgba(255,0,0,0.75)",
              color: "#fff",
              padding: "12px 16px",
              borderRadius: 8,
              fontSize: 20,
              zIndex: 999999,
              maxWidth: 320,
            }}
          >
            <div style={{ fontWeight: "bold" }}>Dev Mode!</div>
          </div>
        )}

        {isDebugMode() && <DebugUtmBox />}

        <main className="grid py-10 min-h-[calc(100vh-64px)] sm:min-h-[calc(100vh-250px)]">
          <div className="h-full self-center">
            <Routes>
              {/* Main Landing Pages */}
              <Route path="/" element={<PreQualifyPage />} />
              <Route path="/ft" element={<FastTrackPage />} />
              {/* Custom Landing Pages */}
              <Route path="/line-of-credit" element={<LineOfCreditPage />} />
              <Route path="/medical-practice-funding" element={<MedicalPracticePage />} />
              <Route path="/ecommerce-funding" element={<EcommercePage />} />
              <Route path="/auto-shop-funding" element={<AutoShopPage />} />
              <Route path="/poor-credit" element={<PoorCreditPage />} />

              {/* Post submission pages */}
              <Route path="/prequalify-result/:uuid" element={lazyLoadPage(PreQualifyResult)} />
              <Route path="/application/:uuid" element={lazyLoadPage(AppFormPage)} />
              <Route path="/application/:uuid/upload" element={lazyLoadPage(UploadPage)} />
              <Route path="/application/:uuid/result" element={lazyLoadPage(ApplicationResult)} />

              {/* Catch-all route for 404 page */}
              <Route path="*" element={lazyLoadPage(NotFoundPage)} />
            </Routes>
          </div>
        </main>
        <Suspense fallback={<FooterSkeleton />}>
          <Footer />
        </Suspense>
      </div>
    </Router>
  );
}

export default App;
