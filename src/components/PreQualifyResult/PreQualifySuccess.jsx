import { Link } from "react-router-dom";
import { trackClick, trackCustomEvent } from "../../utils/analytics.js";
import { Calendar, Phone } from "lucide-react";

/**
 * Pre-qualification success component
 * @param {Object} props - Component props
 * @param {Object} props.application - Application data
 * @returns {JSX.Element}
 */
export const PreQualifySuccess = ({ application }) => {
  // Format currency for display
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const { preQualifyFields, agent } = application;

  // Click tracking handlers
  const handleCallNowClick = () => {
    trackClick("Call Now", {
      phone: agent?.phone,
      advisor: agent?.name,
    });

    trackCustomEvent("rep_phone_click", true);
  };

  const handleScheduleMeetingClick = () => {
    trackClick("Schedule Meeting", {
      calendlyUrl: agent?.calendlyUrl,
      advisor: agent?.name,
    });

    trackCustomEvent("rep_calendar_click", true);
  };

  return (
    <div className="max-w-4xl mx-auto p-3 sm:p-6">
      <div className="bg-white rounded-sm shadow-lg p-4 sm:p-6">
        <div className="text-center mb-2">
          <h2 className="text-xl sm:text-2xl font-bold text-green-600 mb-3 sm:mb-4">
            Congratulations, <span data-hj-allow>{preQualifyFields.firstName}</span>!
          </h2>
          <p className="text-2xl sm:text-3xl text-gray-700 mb-2">You pre-qualify for funding.</p>
        </div>

        <p className="text-base sm:text-lg text-gray-700 mt-5 mb-5 flex flex-col items-center text-center gap-4">
          To complete the process and get your funding faster:
          <Link
            to={`/application/${application.uuid}`}
            className="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-3 px-6 w-full sm:w-auto text-center rounded-sm focus:outline-none focus:shadow-outline text-base sm:text-lg"
            onClick={() => {
              trackCustomEvent("complete_application_click", true);
            }}
          >
            Complete Your Application
          </Link>
        </p>

        {agent && (
          <div className="border-t border-gray-200 py-4 sm:py-6 mt-4 sm:mt-6">
            <p className="text-base sm:text-lg text-center text-gray-600 mb-3">
              Any questions or need personalized support?
              <br />
              Contact your Funding Specialist.
            </p>
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 sm:gap-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full overflow-hidden">
                <img src={agent.image} alt={agent.name} className="w-full h-full object-cover" />
              </div>
              <div className="text-sm sm:text-base text-center md:text-left">
                <h4 className="text-base sm:text-lg font-medium text-gray-800 mb-2">{agent.name}</h4>
                <p className="text-gray-600 mb-1">
                  <span className="font-semibold">Email:</span> <span data-hj-suppress>{agent.email}</span>
                </p>
                <p className="text-gray-600 mb-3 sm:mb-4">
                  <span className="font-semibold">Phone:</span> <span data-hj-suppress>{agent.phone}</span>
                </p>
                <div className="flex flex-col sm:flex-row gap-3 text-sm w-full">
                  <a
                    href={`tel:${agent.phone.replace(/\D/g, "")}`}
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 w-full sm:w-auto"
                    onClick={handleCallNowClick}
                  >
                    <Phone className="inline-block mr-2" size={16} />
                    Call Now
                  </a>
                  <a
                    href={agent.calendlyUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 w-full sm:w-auto"
                    onClick={handleScheduleMeetingClick}
                  >
                    <Calendar className="inline-block mr-2" size={16} />
                    Schedule Meeting
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
        <p className="border-t border-gray-200 pt-6 text-xs text-center text-gray-500">
          Based on the details you provided; final financing terms are available after completing a formal application.
        </p>
      </div>
    </div>
  );
};
