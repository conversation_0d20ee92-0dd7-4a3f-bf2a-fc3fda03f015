import React from "react";
import { FormField } from "../../shared/FormField.jsx";
import { CurrencyField } from "../../shared/CurrencyField.jsx";
import { SelectField } from "../../shared/SelectField.jsx";
import { validationSchema } from "../../../utils/validationSchema";

/**
 * Funding Purpose Options
 */
const purposeOptions = [
  { value: "Expansion", label: "Expansion" },
  { value: "WorkingCapital", label: "Working capital" },
  { value: "Payroll", label: "Payroll" },
  { value: "Equipment", label: "Equipment" },
  { value: "BuyABusiness", label: "Buy a business" },
  { value: "RealEstate", label: "Real estate" },
  { value: "StartABusiness", label: "Start a business" },
  { value: "Other", label: "Other" },
];

/**
 * Top Priority Options
 */
const priorityOptions = [
  { value: "size", label: "Size - Largest amount" },
  { value: "speed", label: "Speed - Fastest approval" },
  { value: "cost", label: "Cost - Lowest rate" },
];

/**
 * Funding Timeline Options
 */
const timelineOptions = [
  { value: "asap", label: "Immediately (Within 48 Hours)" },
  { value: "week", label: "Within a Week" },
  { value: "month", label: "Within a Month" },
  { value: "longterm", label: "Within the Next 6 Months" },
];

/**
 * Funding Information step component
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @returns {JSX.Element}
 */
export const FundingInformation = ({ control, title = "Let’s Figure out your Funding" }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold mb-4">{title}</h3>

      <CurrencyField
        name="fundingAmount"
        label="How much funding do you need?"
        control={control}
        rules={validationSchema.fundingAmount}
        data-hj-allow
      />

      <SelectField
        label="What will you use the funds for?"
        name="purpose"
        options={purposeOptions}
        placeholder="Select purpose..."
        control={control}
        rules={validationSchema.purpose}
        data-hj-allow
      />

      <SelectField
        label="What's your Primary Funding Goal?"
        name="topPriority"
        options={priorityOptions}
        placeholder="Select primary funding goal..."
        control={control}
        rules={validationSchema.topPriority}
        data-hj-allow
      />

      <SelectField
        label="How soon are you looking for funding?"
        name="timeline"
        options={timelineOptions}
        placeholder="Select timeline..."
        control={control}
        rules={validationSchema.timeline}
        data-hj-allow
      />
    </div>
  );
};
