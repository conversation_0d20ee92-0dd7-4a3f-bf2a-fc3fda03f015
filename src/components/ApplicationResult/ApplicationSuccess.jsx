import React from "react";
import { useNavigate } from "react-router-dom";
import { trackClick } from "../../utils/analytics";
import { APP_FLOW_STATUS } from "../../utils/consts";
import { Calendar, Phone, Upload } from "lucide-react";

/**
 * Application success component
 * @param {Object} props - Component props
 * @param {Object} props.application - Application data
 * @returns {JSX.Element}
 */
export const ApplicationSuccess = ({ application }) => {
  const navigate = useNavigate();
  const { applicationFields, agent } = application;
  const firstName = applicationFields?.owners?.[0]?.firstName || "Applicant";
  const businessName = applicationFields?.businessName || "your business";

  return (
    <div className="max-w-4xl mx-auto p-3 sm:p-6">
      <div className="bg-white rounded-sm shadow-lg p-4 sm:p-6">
        <div className="text-center mb-6 sm:mb-8">
          <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
            <svg
              className="w-10 h-10 sm:w-12 sm:h-12 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-green-600 mb-3 sm:mb-4">Application Complete!</h2>
          <p className="text-xl sm:text-2xl text-gray-700 mb-2">Thank you, {firstName}!</p>
          <p className="text-lg sm:text-xl text-gray-700 mb-3 sm:mb-4">
            Your application for <span data-hj-suppress>{businessName}</span> has been submitted successfully.
          </p>
          <p className="text-base sm:text-lg text-gray-600">
            Our team is reviewing your application and will be in touch shortly.
          </p>
        </div>

        {/* Documents Pending Card */}
        {application.status === APP_FLOW_STATUS.APP_DOCS_PENDING && (
          <div className="mt-6 w-full rounded-sm shadow-lg bg-blue-50 border border-blue-200 p-4 sm:p-6">
            <div className="flex items-start space-x-4">
              <div className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 flex-shrink-0">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Upload Bank Statements</h3>
                <p className="text-blue-800 mb-4">
                  Speed up your application processing by uploading your bank statements now.
                </p>
                <button
                  type="button"
                  onClick={() => {
                    trackClick("Upload Bank Statements - Application Success", {
                      uuid: application.uuid,
                    });
                    navigate(`/application/${application.uuid}/upload`);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                >
                  Upload Documents
                </button>
              </div>
            </div>
          </div>
        )}

        {agent && (
          <div className="border-t border-gray-200 py-4 sm:py-6 mt-4 sm:mt-6">
            <p className="text-base sm:text-lg text-center text-gray-600 mb-3">
              Any questions or need personalized support?
              <br />
              Contact your Funding Specialist.
            </p>
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 sm:gap-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full overflow-hidden">
                <img src={agent.image} alt={agent.name} className="w-full h-full object-cover" />
              </div>
              <div className="text-sm sm:text-base text-center md:text-left">
                <h4 className="text-base sm:text-lg font-medium text-gray-800 mb-2">{agent.name}</h4>
                <p className="text-gray-600 mb-1">
                  <span className="font-semibold">Email:</span> <span data-hj-suppress>{agent.email}</span>
                </p>
                <p className="text-gray-600 mb-3 sm:mb-4">
                  <span className="font-semibold">Phone:</span> <span data-hj-suppress>{agent.phone}</span>
                </p>
                <div className="flex flex-col sm:flex-row gap-3 text-sm w-full">
                  <a
                    href={`tel:${agent.phone.replace(/\D/g, "")}`}
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 w-full sm:w-auto"
                    onClick={() => {
                      trackClick("Clicked Call Now - Application Success Page", {
                        uuid: application.uuid,
                        advisor: agent.name,
                      });
                    }}
                  >
                    <Phone className="inline-block mr-2" size={16} />
                    Call Now
                  </a>
                  <a
                    href={agent.calendlyUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white border-2 border-green-600 hover:bg-green-600 hover:text-white text-green-600 text-center font-semibold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition-colors duration-200 w-full sm:w-auto"
                    onClick={() => {
                      trackClick("Clicked Schedule Meeting - Application Success Page", {
                        uuid: application.uuid,
                        advisor: agent.name,
                      });
                    }}
                  >
                    <Calendar className="inline-block mr-2" size={16} />
                    Schedule Meeting
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
