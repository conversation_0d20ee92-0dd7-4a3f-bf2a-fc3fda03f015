import { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { use<PERSON><PERSON>roller } from "react-hook-form";
import { fileToBase64, base64ToFile, isSerializedFile } from "../../utils/fileHelpers";
import { logger } from "../../utils/logger";
import { Upload } from "lucide-react";

// Default values
const DEFAULT_MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
const DEFAULT_FILE_FORMATS = ["application/pdf"];
const DEFAULT_MIN_FILES = 3;
const DEFAULT_MAX_FILES = 6;

/**
 * Reusable file upload component with drag and drop functionality
 * Files are converted to base64 strings for storage in cookies
 * and reconstructed on page reload
 *
 * @param {Object} props - Component props
 * @param {string} props.name - Field name
 * @param {string} props.label - Field label
 * @param {Object} props.control - react-hook-form control object
 * @param {Object} props.rules - Validation rules
 * @param {string[]} props.acceptedFileTypes - Array of accepted MIME types (default: ["application/pdf"])
 * @param {number} props.maxSize - Maximum file size in bytes (default: 2MB)
 * @param {number} props.minFiles - Minimum number of files required (default: 3)
 * @param {number} props.maxFiles - Maximum number of files allowed (default: 6)
 * @returns {JSX.Element}
 */
export const FileUpload = ({
  name,
  label,
  control,
  rules = {},
  acceptedFileTypes = DEFAULT_FILE_FORMATS,
  maxSize = DEFAULT_MAX_FILE_SIZE,
  minFiles = DEFAULT_MIN_FILES,
  maxFiles = DEFAULT_MAX_FILES,
  spanElement = null,
}) => {
  const [fileErrors, setFileErrors] = useState([]);

  const {
    field: { onChange, value = [] },
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
    defaultValue: [],
  });

  // Add a useEffect to handle file reconstruction on component mount
  useEffect(() => {
    // If we have files in the value that are in serialized format (with dataUrl),
    // we need to create preview URLs for them
    if (value && value.length > 0) {
      const filesWithPreviews = value.map((file) => {
        // If the file already has a preview, return it as is
        if (file.preview) return file;

        // If the file has a dataUrl, it's a serialized file
        if (isSerializedFile(file)) {
          try {
            // Create a File object from the dataUrl for display purposes
            const fileObj = base64ToFile(file);
            // Create a preview URL for the file
            return {
              ...file,
              preview: URL.createObjectURL(fileObj),
            };
          } catch (error) {
            logger.error("Error creating preview for file:", error);
            return file;
          }
        }

        return file;
      });

      // Only update if we've added previews
      if (JSON.stringify(filesWithPreviews) !== JSON.stringify(value)) {
        onChange(filesWithPreviews);
      }
    }

    // Cleanup function to revoke object URLs when component unmounts
    return () => {
      if (value) {
        value.forEach((file) => {
          if (file.preview && typeof file.preview === "string") {
            URL.revokeObjectURL(file.preview);
          }
        });
      }
    };
  }, [value, onChange]); // Run when value or onChange changes

  // Helper function to get accepted file types for dropzone
  const getAcceptedFileTypes = () => {
    const acceptObject = {};
    acceptedFileTypes.forEach((mimeType) => {
      switch (mimeType) {
        case "application/pdf":
          acceptObject["application/pdf"] = [".pdf"];
          break;

        default:
          acceptObject[mimeType] = [];
      }
    });
    return acceptObject;
  };

  const onDrop = useCallback(
    async (acceptedFiles, rejectedFiles) => {
      // Handle rejected files
      const errors = rejectedFiles
        .map(({ file, errors }) => {
          return errors.map((e) => {
            if (e.code === "file-too-large") {
              return `${file.name} is too large. Max size is ${maxSize / (1024 * 1024)}MB.`;
            }
            if (e.code === "file-invalid-type") {
              const fileTypeLabels = acceptedFileTypes
                .map((mimeType) => mimeType.split("/")[1]?.toUpperCase() || mimeType)
                .join(", ");
              return `${file.name} has an invalid file type. Only ${fileTypeLabels} files are accepted.`;
            }
            return e.message;
          });
        })
        .flat();

      setFileErrors(errors);

      // If we already have maxFiles, don't add more
      if (value.length >= maxFiles) {
        setFileErrors((prev) => [...prev, `Maximum of ${maxFiles} files allowed.`]);
        return;
      }

      // Add new files to the existing ones, up to maxFiles
      const newFiles = [...value];
      const remainingSlots = maxFiles - newFiles.length;

      // Process each file and convert to serializable format
      const processedFiles = await Promise.all(
        acceptedFiles.slice(0, remainingSlots).map(async (file) => {
          try {
            // Convert file to base64
            const dataUrl = await fileToBase64(file);

            // Create a serializable file object
            return {
              name: file.name,
              type: file.type,
              size: file.size,
              dataUrl,
              // Create a preview URL for display
              preview: URL.createObjectURL(file),
              // Add a timestamp to help with tracking changes
              lastModified: file.lastModified || Date.now(),
            };
          } catch (error) {
            logger.error("Error converting file to base64:", error);
            setFileErrors((prev) => [...prev, `Error processing ${file.name}. Please try again.`]);
            return null;
          }
        }),
      );

      // Filter out any failed conversions
      const validFiles = processedFiles.filter((file) => file !== null);

      // Add the new files to the existing ones
      newFiles.push(...validFiles);

      onChange(newFiles);
    },
    [value, onChange, maxFiles, maxSize, acceptedFileTypes],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedFileTypes(),
    maxSize,
    maxFiles,
  });

  const removeFile = (index) => {
    const newFiles = [...value];

    // Revoke the preview URL to avoid memory leaks
    if (newFiles[index].preview && typeof newFiles[index].preview === "string") {
      URL.revokeObjectURL(newFiles[index].preview);
    }

    newFiles.splice(index, 1);
    onChange(newFiles);
  };

  return (
    <div className="mb-6">
      <label className="block text-gray-700 text-base font-normal mb-2" data-hj-allow>
        {label}
      </label>

      <div
        {...getRootProps()}
        className={`
          bg-white border border-blue-500 rounded-md shadow-sm p-6 text-center cursor-pointer
          flex flex-col items-center transition
          ${isDragActive ? "bg-blue-50 border-blue-600" : "hover:bg-blue-50"}
          ${error ? "border-red-500 bg-red-50" : ""}
        `}
      >
        <input {...getInputProps()} />

        <Upload className="h-8 w-8 text-blue-500 mb-3" />

        <div className="space-y-2">
          <p className="font-semibold text-lg mb-1">
            {isDragActive ? "Drop the files here" : "Drag and drop files here, or click to select"}
          </p>

          {spanElement}
          <p className="mt-4 text-sm text-gray-600 text-center" data-hj-allow>
            {acceptedFileTypes.map((mimeType) => mimeType.split("/")[1]?.toUpperCase() || mimeType).join(", ")} files
            only (max {maxSize / (1024 * 1024)}MB per file)
          </p>
          {/* <p className="text-sm text-gray-600 text-center" data-hj-allow>
            Upload {minFiles} to {maxFiles} files
          </p> */}
        </div>
      </div>

      {/* File errors */}
      {fileErrors.length > 0 && (
        <div className="mt-2">
          {fileErrors.map((err, index) => (
            <p key={index} className="text-red-500 text-xs italic">
              {err}
            </p>
          ))}
        </div>
      )}

      {/* Form validation error */}
      {error && <p className="text-red-500 text-xs italic mt-1">{error.message}</p>}

      {/* File list */}
      {value.length > 0 && (
        <div className="mt-4 space-y-2">
          <p className="text-sm font-medium text-gray-700">Uploaded files:</p>
          <ul className="divide-y divide-gray-200 border border-gray-200 rounded-sm">
            {value.map((file, index) => (
              <li key={index} className="flex items-center justify-between py-3 px-4 hover:bg-gray-50">
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-sm text-gray-700 truncate max-w-xs" data-hj-suppress>
                    {file.name}
                  </span>
                </div>
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 text-sm font-medium"
                >
                  Remove
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
