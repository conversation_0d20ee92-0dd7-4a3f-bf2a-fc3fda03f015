import { useState, useEffect } from "react";
import { FileUpload } from "../../shared/FileUpload.jsx";
import { validationSchema } from "../../../utils/validationSchema";
import { Upload, Mail } from "lucide-react";

/**
 * Bank Statements step component
 *
 * @param {Object} props
 * @param {Object} props.control - react-hook-form control object
 * @param {Function} props.onSkip - Function to handle skipping this step
 * @returns {JSX.Element}
 */
export const BankStatements = ({ control, onSkip, onReadyToSubmit }) => {
  const [showUpload, setShowUpload] = useState(false);

  // Notify parent when ready to submit (showUpload is true or skip is chosen)
  useEffect(() => {
    if (onReadyToSubmit) {
      onReadyToSubmit(showUpload);
    }
  }, [showUpload, onReadyToSubmit]);

  if (!showUpload) {
    return <BankStatementSelector setShowUpload={setShowUpload} onSkip={onSkip} />;
  }

  return (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold mb-4">Upload Bank Statements</h3>

      <p className="text-gray-600 mb-6" data-hj-allow>
        Please upload your last 3 months of business bank statements.
      </p>

      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-blue-700">For faster processing, please ensure:</p>
            <ul className="list-disc list-inside text-sm text-blue-700 mt-1" data-hj-allow>
              <li>Files are in PDF format</li>
              <li>Each file is less than 2MB</li>
              <li>Statements show your business name and account number</li>
              <li>Statements are for the most recent 3 months</li>
            </ul>
          </div>
        </div>
      </div>

      <FileUpload
        name="bankStatements"
        label="Bank Statements"
        control={control}
        rules={validationSchema.bankStatements}
        acceptedFileTypes={["application/pdf"]}
        maxSize={2 * 1024 * 1024} // 2MB
        minFiles={3}
        maxFiles={6}
      />

      <div className="mt-4 text-sm text-gray-500">
        <p>Your bank statements are securely stored and will only be used for underwriting purposes.</p>
        <button
          type="button"
          onClick={() => {
            setShowUpload(false);
            if (onSkip) onSkip();
          }}
          className="text-blue-600 underline hover:text-blue-800 focus:outline-none mt-2"
        >
          Skip this step, I'll email them manually
        </button>
      </div>
    </div>
  );
};

const BankStatementSelector = ({ setShowUpload, onSkip }) => {
  return (
    <div className="max-w-2xl mx-auto py-5">
      <h3 className="text-2xl font-semibold mb-8 text-center">Last Step: Submit Your Bank Statements</h3>
      <p className="text-gray-700 mb-3 text-center max-w-xl mx-auto">
        We need your last 3 months of business bank statements to complete the process.
      </p>
      <p className="text-gray-700 mb-8 text-center max-w-xl mx-auto">
        Your application cannot be processed without the bank statements.
      </p>
      <div className="flex flex-col md:flex-row gap-6 justify-center">
        {/* Upload Now Option */}
        <button
          className="flex-1 bg-white border border-blue-500 rounded-md shadow-sm p-6 flex flex-col items-center hover:bg-blue-50 transition"
          onClick={() => setShowUpload(true)}
          type="button"
        >
          <Upload className="h-8 w-8 text-blue-500 mb-3" />
          <span className="font-semibold text-lg mb-1">Upload Now</span>
          <span className="text-sm text-gray-600 text-center">
            Secure upload of PDF bank statements for fastest processing.
          </span>
          <span className="bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-semibold mt-4">
            Fastest Approval
          </span>
        </button>
        {/* Email Later Option */}
        <button
          className="flex-1 bg-white border border-gray-300 rounded-md shadow-sm p-6 flex flex-col items-center hover:bg-gray-50 transition"
          onClick={onSkip}
          type="button"
        >
          <Mail className="h-8 w-8 text-gray-400 mb-3" />
          <span className="font-semibold text-lg mb-1">Email Later</span>
          <span className="text-sm text-gray-600 text-center">
            Prefer to email them? Your application will be on hold until we receive them.
          </span>
          <span className="bg-gray-100 text-gray-600 rounded-full px-3 py-1 text-xs font-semibold mt-4">
            Manual Processing
          </span>
        </button>
      </div>
    </div>
  );
};
